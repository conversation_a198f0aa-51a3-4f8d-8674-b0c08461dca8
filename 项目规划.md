# 旧书买卖平台项目规划

## 项目概述

### 项目名称
大学生旧书买卖平台

### 项目目标
- 为大学生提供便捷的二手书买卖平台
- 采用B2C模式，平台收购后销售，后续扩展C2C功能
- 建立小型社区化的图书交易生态

### 目标用户
- 主要用户：大学生群体
- 用户规模：小型社区（预计500-2000用户）
- 使用场景：教材买卖、课外读物交换

## 技术栈选择

### 前端技术栈
- **框架**: React 18 + Vite
- **UI库**: Ant Design / Material-UI
- **状态管理**: Redux Toolkit / Zustand
- **路由**: React Router v6
- **HTTP客户端**: Axios
- **实时通信**: Socket.io-client

### 后端技术栈
- **运行环境**: Node.js 18+
- **框架**: Express.js
- **数据库**: PostgreSQL
- **ORM**: Prisma / Sequelize
- **身份验证**: JWT + bcrypt
- **短信服务**: 阿里云短信服务 / 腾讯云短信
- **第三方登录**: 微信开放平台SDK + QQ互联SDK
- **实时通信**: Socket.io
- **文件上传**: Multer + 云存储

### 开发工具
- **版本控制**: Git
- **包管理**: npm/yarn
- **代码规范**: ESLint + Prettier
- **容器化**: Docker
- **API文档**: Swagger/OpenAPI

## 核心功能模块

### 1. 用户管理模块
- **用户注册/登录**
  - 手机号注册（主要方式，后续开发短信验证码）
  - 邮箱注册验证（备用方式）
  - 密码找回功能
  - 第三方登录（微信/QQ，点击后跳转到对应平台登录页面）

- **用户角色**
  - 普通用户（买家）
  - 管理员（图书录入、订单管理，可有多个，由超级管理员创建）
  - 超级管理员（系统管理、管理员账号管理、操作监控）

### 2. 图书管理模块
- **图书录入**（管理员功能）
  - ISBN扫码录入
  - 手动信息录入
  - 图书信息自动补全（调用图书API）
  - 图书照片上传
  - 价格设定
  - 库存管理

- **图书信息展示**
  - 图书详情页
  - 图书列表页
  - 分类浏览
  - 搜索功能（标题、作者、ISBN）
  - 筛选功能（价格、分类、状况）

### 3. 交易流程模块
- **购买流程**
  - 加入购物车
  - 订单确认
  - 支付选择（线上/线下）
  - 配送方式选择

- **退货流程**
  - 两天无理由退货政策（从送达时间开始计算）
  - 用户申请退货并填写退货原因
  - 系统显示管理员联系电话
  - 用户电话联系管理员协商处理
  - 管理员审核并处理退货申请

- **订单管理**
  - 订单状态跟踪（待支付/已支付/配送中/已送达）
  - 配送中显示配送员联系电话
  - 订单历史查看
  - 退货处理（两天无理由退货，电话联系管理员协商）

### 4. 支付模块
- **支付方式**
  - 线上支付（支付宝/微信支付）
  - 线下支付（现金/转账）
  - 支付状态管理

### 5. 物流配送模块
- **配送方式**
  - 平台配送（当前）
  - 自取功能（后期）

- **配送状态管理**
  - 配送中（显示配送员联系电话）
  - 已送达（配送完成）

### 6. 消息通信模块
- **实时聊天**
  - 用户与管理员沟通
  - 订单相关咨询
  - 系统通知推送

- **图书消息功能**
  - 用户可在图书详情页发送消息咨询
  - 管理员接收并回复图书相关问题
  - 消息显示在图书详情页的评论区

- **多种联系方式**
  - 用户可选择联系方式：在线消息、微信、QQ、电话
  - 管理员联系方式展示（微信号、QQ号、电话号码）
  - 用户可根据偏好选择沟通方式

- **消息管理功能**（管理员专用）
  - 管理员可删除不当消息
  - 消息状态管理（显示/隐藏）
  - 消息内容审核和处理

### 7. 管理员功能模块（普通管理员 + 超级管理员）

#### 7.1 管理员仪表板
- **工作概况**
  - 今日待处理订单数量
  - 今日新增图书数量
  - 待回复消息数量
  - 个人操作统计

- **快速操作面板**
  - 添加新图书快捷入口
  - 待处理订单快捷查看
  - 紧急消息快捷回复
  - 库存预警提醒

- **最近活动**
  - 最近操作记录
  - 最近处理的订单
  - 最近回复的消息
  - 系统通知

#### 7.2 图书管理模块
- **图书列表管理**
  - 图书信息表格（书名、作者、ISBN、价格、库存、状态、操作时间）
  - 高级搜索功能（按书名、作者、ISBN、分类、价格范围搜索）
  - 多条件筛选（分类、状态、价格区间、库存状态）
  - 批量操作（批量上架/下架、批量价格调整、批量删除）
  - 导出功能（Excel格式导出图书列表）

- **图书录入功能**
  - ISBN扫码录入（调用图书API自动填充信息）
  - 手动信息录入表单（书名、作者、出版社、分类、描述）
  - 图书图片上传（封面图、实物图，支持多图上传）
  - 价格设定和库存管理
  - 图书状态设置（上架/下架/预售）

- **图书编辑功能**
  - 图书信息修改（所有字段可编辑）
  - 价格历史记录查看
  - 库存变动记录
  - 图片管理（添加、删除、排序）
  - 图书状态变更记录

- **库存管理**
  - 库存预警设置（低库存提醒）
  - 库存变动记录（入库、出库、调整）
  - 批量库存调整
  - 库存统计报表

#### 7.3 订单管理模块
- **订单列表管理**
  - 订单信息表格（订单号、用户、商品、金额、状态、下单时间）
  - 订单状态筛选标签（全部、待支付、已支付、配送中、已完成、已取消、退货中）
  - 订单搜索功能（按订单号、用户名、商品名搜索）
  - 时间范围筛选（今天、昨天、本周、本月、自定义）
  - 批量操作（批量确认、批量发货、批量取消）

- **订单详情管理**
  - 订单完整信息查看（用户信息、商品详情、支付信息、配送信息）
  - 订单状态更新（手动更新订单状态）
  - 订单备注添加（内部备注，用户不可见）
  - 订单操作历史查看
  - 订单打印功能（发货单、收据）

- **支付管理**
  - 支付状态查看和更新
  - 线下支付确认
  - 退款处理（部分退款、全额退款）
  - 支付异常处理

- **退货处理**
  - 退货申请列表查看
  - 退货原因审核
  - 退货状态更新（同意/拒绝退货）
  - 退货商品状态检查
  - 退款处理流程

#### 7.4 配送管理模块
- **配送订单管理**
  - 待配送订单列表
  - 配送状态跟踪
  - 配送批次管理
  - 配送路线规划

- **配送员管理**
  - 配送员信息管理（姓名、电话、状态）
  - 配送员工作量统计
  - 配送员绩效评估
  - 配送任务分配

- **配送状态更新**
  - 配送状态实时更新
  - 配送异常处理
  - 配送完成确认
  - 用户配送反馈查看

#### 7.5 消息管理模块
- **用户消息管理**
  - 用户消息列表（按图书、订单分类）
  - 消息状态筛选（未读、已读、已回复）
  - 消息搜索功能
  - 批量消息操作

- **消息回复功能**
  - 消息详情查看
  - 快速回复模板
  - 富文本回复编辑
  - 图片回复支持
  - 回复历史记录

- **消息审核功能**
  - 不当消息识别
  - 消息删除/隐藏
  - 消息举报处理
  - 违规用户处理

#### 7.6 个人设置模块
- **个人信息管理**
  - 基本信息编辑（姓名、邮箱、手机号）
  - 头像上传和更换
  - 密码修改功能
  - 登录历史查看

- **联系方式设置**
  - 微信号设置（用户可见）
  - QQ号设置（用户可见）
  - 公开电话设置（用户可见）
  - 联系方式显示开关

- **工作偏好设置**
  - 通知设置（邮件通知、系统通知）
  - 工作时间设置
  - 自动回复设置
  - 界面主题设置

### 8. 超级管理员专用模块（继承所有管理员功能）

#### 8.1 超级管理员仪表板（Dashboard）
- **系统概况**
  - 实时在线用户数统计
  - 管理员在线状态显示
  - 今日/本周/本月订单统计
  - 系统资源使用情况（CPU、内存、存储）
  - 关键业务指标卡片展示

- **数据可视化**
  - 用户注册趋势图（折线图）
  - 订单量统计图（柱状图）
  - 图书分类销售占比（饼图）
  - 管理员操作活跃度热力图

- **实时监控**
  - 最近操作日志流（实时更新）
  - 系统异常告警面板
  - 在线用户活动监控
  - 服务器状态监控

#### 8.2 管理员账号管理
- **管理员列表管理**
  - 管理员信息表格（用户名、角色、状态、创建时间、最后登录）
  - 高级搜索筛选（按角色、状态、创建时间筛选）
  - 批量操作（批量启用/禁用、批量删除）
  - 导出管理员列表功能

- **创建管理员账号**
  - 管理员信息表单（用户名、密码、邮箱、手机号）
  - 用户名唯一性实时验证
  - 联系方式设置（微信、QQ、公开电话）
  - 角色权限分配
  - 账号有效期设置

- **管理员详情管理**
  - 管理员个人信息查看和编辑
  - 密码重置功能（发送重置邮件）
  - 账号状态管理（启用/禁用/锁定）
  - 登录历史查看（IP地址、登录时间、设备信息）
  - 操作历史统计（操作次数、操作类型分布）

- **权限管理系统**
  - 角色权限矩阵设置
  - 功能模块权限分配（图书管理、订单管理、用户管理等）
  - 数据权限控制（可访问的数据范围）
  - 操作权限设置（增删改查权限）
  - 权限继承和覆盖规则

#### 8.3 操作监控系统
- **实时操作监控**
  - 管理员操作实时日志流
  - 操作类型分类显示（登录、图书操作、订单操作等）
  - 危险操作高亮提醒
  - 操作频率监控和告警

- **操作日志管理**
  - 详细操作日志查询（支持多条件组合查询）
  - 日志时间范围筛选（今天、昨天、本周、本月、自定义）
  - 操作类型筛选（创建、更新、删除、登录、登出）
  - 操作对象筛选（图书、订单、用户、消息）
  - 操作结果筛选（成功、失败）

- **数据统计分析**
  - 管理员操作统计报表
  - 操作趋势分析图表
  - 操作效率分析（平均处理时间）
  - 错误操作统计和分析
  - 工作量统计（每日/每周操作量）

- **异常检测和告警**
  - 异常操作模式检测（频繁失败、异常时间操作）
  - 安全风险告警（异地登录、密码多次错误）
  - 系统性能告警（响应时间过长、错误率过高）
  - 业务异常告警（大量退货、异常订单）
  - 告警通知设置（邮件、短信、系统通知）

#### 8.4 系统管理
- **系统配置管理**
  - 系统基础配置（网站名称、Logo、联系方式）
  - 业务规则配置（退货天数、配送费用、手续费率）
  - 支付配置管理（支付宝、微信支付参数）
  - 短信服务配置（短信模板、发送频率限制）
  - 第三方服务配置（图书API、云存储配置）

- **数据管理**
  - 数据库备份和恢复
  - 数据清理策略设置（日志保留期、临时文件清理）
  - 数据导入导出功能
  - 数据完整性检查
  - 数据统计报表生成

- **系统维护**
  - 系统性能监控（响应时间、并发数、错误率）
  - 服务器资源监控（CPU、内存、磁盘、网络）
  - 定时任务管理（备份任务、清理任务、统计任务）
  - 系统日志查看（错误日志、访问日志、性能日志）
  - 缓存管理（缓存清理、缓存统计）

#### 8.5 业务数据分析
- **用户数据分析**
  - 用户注册趋势分析
  - 用户活跃度统计（日活、周活、月活）
  - 用户行为分析（浏览、搜索、购买路径）
  - 用户地域分布统计
  - 用户留存率分析

- **图书数据分析**
  - 图书销售排行榜
  - 图书分类销售统计
  - 图书库存预警
  - 图书价格趋势分析
  - 热门搜索关键词统计

- **订单数据分析**
  - 订单量趋势分析（日、周、月）
  - 订单金额统计
  - 订单状态分布
  - 退货率统计和分析
  - 配送效率分析

- **财务数据分析**
  - 收入统计报表
  - 支付方式使用统计
  - 退款统计分析
  - 成本分析（配送成本、运营成本）
  - 利润分析报表

#### 8.6 内容审核管理
- **消息内容审核**
  - 待审核消息列表
  - 消息内容违规检测
  - 批量审核操作
  - 审核历史记录
  - 违规内容统计

- **图书信息审核**
  - 新增图书信息审核
  - 图书信息修改审核
  - 图书图片审核
  - 价格合理性检查
  - 重复图书检测

- **用户举报处理**
  - 用户举报列表管理
  - 举报内容分类处理
  - 举报处理结果跟踪
  - 恶意举报识别
  - 举报统计分析

## 管理员界面设计详情

### 管理员界面整体布局

#### 界面结构：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏                                               │
│ [Logo] [导航菜单] [用户信息] [通知] [退出]                 │
├─────────────────────────────────────────────────────────┤
│ 侧边栏导航 │ 主内容区域                                  │
│            │ ┌─────────────────────────────────────────┐ │
│ [仪表板]   │ │ 面包屑导航                               │ │
│ [图书管理] │ │ ┌─────────────────────────────────────┐ │ │
│ [订单管理] │ │ │                                     │ │ │
│ [配送管理] │ │ │        页面内容区域                  │ │ │
│ [消息管理] │ │ │                                     │ │ │
│ [个人设置] │ │ │                                     │ │ │
│            │ │ └─────────────────────────────────────┘ │ │
│            │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 导航菜单设计（根据权限显示）：

**普通管理员菜单**：
```
├── 仪表板
├── 图书管理
│   ├── 图书列表
│   ├── 添加图书
│   ├── 库存管理
│   └── 分类管理
├── 订单管理
│   ├── 订单列表
│   ├── 退货处理
│   └── 订单统计
├── 配送管理
│   ├── 配送订单
│   ├── 配送员管理
│   └── 配送统计
├── 消息管理
│   ├── 用户消息
│   ├── 消息回复
│   └── 消息审核
└── 个人设置
    ├── 个人信息
    ├── 联系方式
    └── 工作偏好
```

**超级管理员菜单**（包含所有管理员功能 + 专用功能）：
```
├── 仪表板（超级版）
├── 图书管理（继承）
├── 订单管理（继承）
├── 配送管理（继承）
├── 消息管理（继承）
├── 管理员管理
├── 操作监控
├── 系统管理
├── 数据分析
├── 内容审核
└── 个人设置（继承）
```

### 核心页面界面设计

#### 1. 管理员仪表板页面
```
┌─────────────────────────────────────────────────────────┐
│ 工作概况卡片区                                           │
│ [待处理订单: 12] [新增图书: 5] [待回复消息: 8] [库存预警: 3] │
├─────────────────────────────────────────────────────────┤
│ 快速操作区                                               │
│ [添加图书] [处理订单] [回复消息] [查看预警]               │
├─────────────────────────────────────────────────────────┤
│ 最近活动区                                               │
│ ┌─────────────────┐ ┌─────────────────────────────────┐ │
│ │ 最近操作        │ │ 待处理事项                       │ │
│ │ • 添加图书《...》│ │ • 订单#001 待确认               │ │
│ │ • 处理订单#002  │ │ • 用户张三咨询图书价格           │ │
│ │ • 回复用户消息  │ │ • 《数学教材》库存不足           │ │
│ └─────────────────┘ └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 2. 图书管理页面
```
┌─────────────────────────────────────────────────────────┐
│ 操作栏                                                   │
│ [添加图书] [批量上架] [批量下架] [导出] [刷新]           │
├─────────────────────────────────────────────────────────┤
│ 搜索筛选区                                               │
│ 搜索: [___________] 分类: [下拉选择] 状态: [下拉选择]     │
│ 价格: [最低___] - [最高___] [搜索] [重置]                │
├─────────────────────────────────────────────────────────┤
│ 图书列表表格                                             │
│ ☑ │书名    │作者  │ISBN      │分类│价格│库存│状态│操作  │
│ ☑ │数学教材│张三  │978...    │教材│50  │10  │上架│编辑删除│
│ ☑ │英语词典│李四  │978...    │工具│30  │5   │上架│编辑删除│
│ ☑ │小说集  │王五  │978...    │文学│25  │0   │缺货│编辑删除│
├─────────────────────────────────────────────────────────┤
│ 分页控制                                                 │
│ 共100条 [上一页] 1 2 3 4 5 [下一页] 每页显示: [20条]    │
└─────────────────────────────────────────────────────────┘
```

#### 3. 订单管理页面
```
┌─────────────────────────────────────────────────────────┐
│ 状态筛选标签                                             │
│ [全部(50)] [待支付(5)] [已支付(20)] [配送中(15)] [已完成(8)] [退货(2)] │
├─────────────────────────────────────────────────────────┤
│ 搜索区                                                   │
│ 订单号: [_______] 用户: [_______] 时间: [日期选择器]     │
├─────────────────────────────────────────────────────────┤
│ 订单列表                                                 │
│ 订单号    │用户  │商品      │金额│状态  │时间    │操作    │
│ #20240001 │张三  │数学教材  │50  │已支付│10:30   │查看详情│
│ #20240002 │李四  │英语词典  │30  │配送中│09:15   │查看详情│
│ #20240003 │王五  │小说集    │25  │待支付│08:45   │查看详情│
├─────────────────────────────────────────────────────────┤
│ 订单详情侧边栏（点击查看详情时显示）                     │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 订单详情 #20240001                                  │ │
│ │ 用户：张三 (138****1234)                           │ │
│ │ 商品：数学教材 x1                                   │ │
│ │ 金额：￥50.00                                      │ │
│ │ 状态：已支付                                        │ │
│ │ 地址：北京市朝阳区...                              │ │
│ │ [确认发货] [联系用户] [取消订单]                    │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 4. 消息管理页面
```
┌─────────────────────────────────────────────────────────┐
│ 消息筛选                                                 │
│ 类型: [全部] [图书咨询] [订单咨询] 状态: [未读] [已读]   │
├─────────────────────────────────────────────────────────┤
│ 消息列表                                                 │
│ 用户  │类型    │内容预览        │时间    │状态│操作      │
│ 张三  │图书咨询│这本书还有库存吗│10:30   │未读│回复 删除  │
│ 李四  │订单咨询│什么时候发货    │09:15   │已读│回复 删除  │
│ 王五  │图书咨询│能便宜点吗      │08:45   │已回复│查看 删除│
├─────────────────────────────────────────────────────────┤
│ 消息详情和回复区                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 用户张三 10:30                                      │ │
│ │ 这本《数学教材》还有库存吗？什么时候能发货？         │ │
│ │                                                     │ │
│ │ 管理员回复：                                        │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ [快速回复模板▼] [表情] [图片]                   │ │ │
│ │ │ ┌─────────────────────────────────────────────┐ │ │ │
│ │ │ │ 您好，这本书目前有库存，今天下单明天发货    │ │ │ │
│ │ │ └─────────────────────────────────────────────┘ │ │ │
│ │ │ [发送] [保存模板]                               │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 响应式设计适配

#### 移动端适配：
- 侧边栏导航改为顶部下拉菜单
- 表格改为卡片式布局
- 操作按钮合并为更多操作菜单
- 搜索筛选改为抽屉式面板

#### 平板端适配：
- 保持侧边栏导航，但可收缩
- 表格保持，但列数减少
- 详情面板改为模态框显示

## 数据库设计

### 核心数据表

#### 用户表 (users)
```sql
- id (主键)
- username (用户名，管理员账号唯一)
- email (邮箱，可选)
- phone (手机号，主要注册方式)
- password_hash (密码哈希)
- role (角色: user/admin/super_admin)
- avatar (头像URL)
- register_type (注册类型: phone/email/wechat/qq)
- third_party_id (第三方平台用户ID，用于微信/QQ登录)
- phone_verified (手机号验证状态)
- email_verified (邮箱验证状态)
- status (账户状态: active/inactive/banned)
- created_by (创建者ID，记录是哪个超级管理员创建的管理员账号)
- contact_wechat (微信号，管理员联系方式)
- contact_qq (QQ号，管理员联系方式)
- contact_phone_public (公开联系电话，管理员联系方式)
- last_login_at (最后登录时间)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 图书表 (books)
```sql
- id (主键)
- isbn (ISBN号)
- title (书名)
- author (作者)
- publisher (出版社)
- publication_date (出版日期)
- category_id (分类ID)
- description (描述)
- condition (书籍状况)
- price (价格)
- stock (库存)
- cover_image (封面图片)
- images (其他图片)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 订单表 (orders)
```sql
- id (主键)
- user_id (用户ID)
- total_amount (总金额)
- status (订单状态: pending/paid/delivering/delivered/return_requested/returned/cancelled)
- payment_method (支付方式)
- payment_status (支付状态)
- delivery_method (配送方式)
- delivery_address (配送地址)
- delivery_status (配送状态: pending/delivering/delivered)
- delivery_phone (配送员联系电话)
- delivery_person (配送员姓名)
- return_reason (退货原因)
- return_status (退货状态: none/requested/approved/rejected/completed)
- return_requested_at (退货申请时间)
- notes (备注)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 订单详情表 (order_items)
```sql
- id (主键)
- order_id (订单ID)
- book_id (图书ID)
- quantity (数量)
- price (单价)
- subtotal (小计)
```

#### 分类表 (categories)
```sql
- id (主键)
- name (分类名称)
- description (描述)
- parent_id (父分类ID)
```

#### 消息表 (messages)
```sql
- id (主键)
- sender_id (发送者ID)
- receiver_id (接收者ID，可选，图书消息时为空)
- book_id (关联图书ID，图书消息专用)
- order_id (关联订单ID，可选)
- content (消息内容)
- type (消息类型: chat/book_inquiry/order_inquiry/system_notification)
- status (消息状态: active/hidden/deleted)
- read_status (已读状态)
- is_admin_reply (是否为管理员回复)
- parent_message_id (父消息ID，用于回复功能)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 管理员操作日志表 (admin_logs)
```sql
- id (主键)
- admin_id (管理员ID)
- action_type (操作类型: create/update/delete/login/logout)
- target_type (操作对象类型: book/order/user)
- target_id (操作对象ID)
- action_description (操作描述)
- ip_address (操作IP地址)
- user_agent (用户代理)
- request_data (请求数据，JSON格式)
- result (操作结果: success/failed)
- created_at (操作时间)
```

#### 系统配置表 (system_configs)
```sql
- id (主键)
- config_key (配置键名)
- config_value (配置值)
- config_type (配置类型: string/number/boolean/json)
- description (配置描述)
- is_public (是否公开配置)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 系统告警表 (system_alerts)
```sql
- id (主键)
- alert_type (告警类型: security/performance/business/system)
- alert_level (告警级别: low/medium/high/critical)
- title (告警标题)
- description (告警描述)
- target_type (关联对象类型)
- target_id (关联对象ID)
- status (处理状态: pending/processing/resolved/ignored)
- created_at (创建时间)
- resolved_at (解决时间)
- resolved_by (处理人ID)
```

#### 数据统计表 (statistics)
```sql
- id (主键)
- stat_type (统计类型: user/order/book/revenue)
- stat_date (统计日期)
- stat_period (统计周期: daily/weekly/monthly)
- stat_data (统计数据，JSON格式)
- created_at (创建时间)
```

#### 审核记录表 (audit_records)
```sql
- id (主键)
- audit_type (审核类型: message/book/user_report)
- target_type (审核对象类型)
- target_id (审核对象ID)
- auditor_id (审核员ID)
- audit_result (审核结果: approved/rejected/pending)
- audit_reason (审核原因)
- audit_notes (审核备注)
- created_at (创建时间)
- updated_at (更新时间)
```

## 开发阶段规划

### 第一阶段：MVP核心功能（2-3周）
**目标**: 实现基础的图书买卖功能

**功能清单**:
- [ ] 项目初始化和环境搭建
- [ ] 用户注册/登录系统
- [ ] 基础图书管理（录入、展示）
- [ ] 简单搜索功能
- [ ] 基础购买流程
- [ ] 订单管理基础功能（包含退货申请）
- [ ] 基础支付集成
- [ ] 退货处理系统（两天无理由退货）
- [ ] 管理员基础界面开发（仪表板、图书管理、订单管理）

**技术任务**:
- [ ] 数据库设计和创建
- [ ] 后端API基础架构
- [ ] 前端项目初始化
- [ ] 用户认证系统（手机号注册 + 第三方登录跳转）
- [ ] 短信服务集成（后期开发验证码）
- [ ] 微信/QQ第三方登录SDK集成
- [ ] 图书信息API集成

### 第二阶段：功能完善（2-3周）
**目标**: 完善用户体验和管理功能

**功能清单**:
- [ ] 高级搜索和筛选
- [ ] 用户个人中心
- [ ] 完整订单管理系统
- [ ] 实时聊天功能
- [ ] 图书消息功能（用户咨询、管理员回复）
- [ ] 多种联系方式功能（在线消息/微信/QQ/电话选择）
- [ ] 消息管理功能（管理员删除、审核消息）
- [ ] 管理员联系方式管理（设置和更新联系信息）
- [ ] 图书分类管理
- [ ] 管理员完整功能开发（配送管理、消息管理、个人设置）
- [ ] 超级管理员仪表板开发
- [ ] 管理员账号管理系统（超级管理员功能）
- [ ] 管理员操作监控和日志系统
- [ ] 系统管理功能（配置、数据、维护）
- [ ] 业务数据分析系统
- [ ] 内容审核管理系统
- [ ] 实时监控和告警系统
- [ ] 管理员界面完善和优化
- [ ] 支付功能完善

### 第三阶段：优化和扩展（1-2周）
**目标**: 性能优化和功能扩展

**功能清单**:
- [ ] 性能优化
- [ ] C2C功能基础架构
- [ ] 自取功能开发
- [ ] 移动端适配
- [ ] 云部署准备

## 图书信息数据源

### 推荐API服务
1. **豆瓣图书API** (主要)
   - 丰富的图书信息
   - 包含评分和评论
   - 免费使用（有限制）

2. **开放图书馆API** (备用)
   - Open Library API
   - 国际图书数据

3. **ISBN查询服务** (补充)
   - 基础图书信息查询
   - 多个免费服务可选

## 用户界面设计详情

### 设计系统规范

#### 色彩系统
```
主色调：
- 主色：#1890ff (蓝色，代表知识和信任)
- 主色浅：#40a9ff
- 主色深：#096dd9

辅助色：
- 成功色：#52c41a (绿色)
- 警告色：#faad14 (橙色)
- 错误色：#ff4d4f (红色)
- 信息色：#1890ff (蓝色)

中性色：
- 标题色：#262626
- 正文色：#595959
- 说明色：#8c8c8c
- 边框色：#d9d9d9
- 背景色：#fafafa
- 白色：#ffffff
```

#### 字体系统
```
字体族：
- 中文：'PingFang SC', 'Microsoft YaHei', sans-serif
- 英文：'Segoe UI', 'Roboto', sans-serif

字号规范：
- 大标题：28px/32px (h1)
- 标题：24px/28px (h2)
- 小标题：20px/24px (h3)
- 正文：16px/24px (body)
- 小字：14px/20px (small)
- 说明：12px/16px (caption)
```

#### 间距系统
```
基准单位：4px

常用间距：
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px
- xxl: 48px
```

#### 组件规范
```
按钮：
- 主按钮：高度40px，圆角6px，主色背景
- 次按钮：高度40px，圆角6px，白色背景+主色边框
- 文字按钮：无背景，主色文字

输入框：
- 高度40px，圆角6px，边框色#d9d9d9
- 聚焦时边框色变为主色

卡片：
- 圆角8px，阴影0 2px 8px rgba(0,0,0,0.1)
- 内边距16px
```

### 响应式断点设计
```
断点定义：
- xs: <576px (手机竖屏)
- sm: 576px-768px (手机横屏)
- md: 768px-992px (平板)
- lg: 992px-1200px (小桌面)
- xl: 1200px-1600px (大桌面)
- xxl: >1600px (超大屏)

布局适配：
- 手机端：单列布局，底部导航
- 平板端：两列布局，侧边导航
- 桌面端：多列布局，顶部+侧边导航
```

### 核心页面设计详情

#### 1. 首页设计

**整体布局**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏                                               │
│ [Logo] [搜索框] [分类] [登录] [注册] [购物车]             │
├─────────────────────────────────────────────────────────┤
│ 轮播Banner区域                                           │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [推荐图书/活动信息轮播图]                           │ │
│ │ • • • (指示器)                                      │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 快速分类导航                                             │
│ [教材📚] [文学📖] [工具书🔧] [考试📝] [其他📋]           │
├─────────────────────────────────────────────────────────┤
│ 推荐图书区域                                             │
│ 热门图书 [查看更多>]                                     │
│ ┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐                │
│ │[图片] │ │[图片] │ │[图片] │ │[图片] │                │
│ │书名   │ │书名   │ │书名   │ │书名   │                │
│ │￥价格 │ │￥价格 │ │￥价格 │ │￥价格 │                │
│ └───────┘ └───────┘ └───────┘ └───────┘                │
│                                                         │
│ 新品上架 [查看更多>]                                     │
│ ┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐                │
│ │[图片] │ │[图片] │ │[图片] │ │[图片] │                │
│ │书名   │ │书名   │ │书名   │ │书名   │                │
│ │￥价格 │ │￥价格 │ │￥价格 │ │￥价格 │                │
│ └───────┘ └───────┘ └───────┘ └───────┘                │
├─────────────────────────────────────────────────────────┤
│ 底部信息                                                 │
│ 联系我们 | 帮助中心 | 关于我们                           │
└─────────────────────────────────────────────────────────┘
```

**顶部导航栏详细设计**：
```
桌面端 (1200px+)：
┌─────────────────────────────────────────────────────────┐
│ [Logo] [搜索框________________] [分类▼] [登录] [注册] [🛒3] │
└─────────────────────────────────────────────────────────┘

移动端 (<768px)：
┌─────────────────────────────────────────────────────────┐
│ [☰] [Logo] [🔍] [🛒3]                                    │
├─────────────────────────────────────────────────────────┤
│ [搜索框_________________________]                       │
└─────────────────────────────────────────────────────────┘
```

**图书卡片组件设计**：
```
┌─────────────────┐
│ ┌─────────────┐ │ <- 图书封面 (160x200px)
│ │   [图片]    │ │
│ │             │ │
│ └─────────────┘ │
│ 数学分析教程    │ <- 书名 (16px, 最多2行)
│ 作者：张三      │ <- 作者 (14px, 灰色)
│ ⭐⭐⭐⭐⭐ 4.8   │ <- 评分 (可选)
│ ￥45.00        │ <- 价格 (18px, 主色)
│ 原价：￥89.00   │ <- 原价 (14px, 删除线)
│ [加入购物车]    │ <- 操作按钮
└─────────────────┘
```

#### 2. 图书列表页设计

**整体布局**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (同首页)                                      │
├─────────────────────────────────────────────────────────┤
│ 面包屑导航                                               │
│ 首页 > 图书分类 > 教材类                                 │
├─────────────────────────────────────────────────────────┤
│ 筛选和排序栏                                             │
│ 筛选: [价格▼] [分类▼] [状况▼] | 排序: [综合▼] [🔄]      │
├─────────────────────────────────────────────────────────┤
│ 侧边栏筛选 │ 图书网格区域                                │
│            │ ┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐    │
│ 价格范围   │ │[图书] │ │[图书] │ │[图书] │ │[图书] │    │
│ ○ 0-20元   │ │ 卡片  │ │ 卡片  │ │ 卡片  │ │ 卡片  │    │
│ ○ 21-50元  │ └───────┘ └───────┘ └───────┘ └───────┘    │
│ ○ 51-100元 │ ┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐    │
│            │ │[图书] │ │[图书] │ │[图书] │ │[图书] │    │
│ 图书状况   │ │ 卡片  │ │ 卡片  │ │ 卡片  │ │ 卡片  │    │
│ ☑ 全新     │ └───────┘ └───────┘ └───────┘ └───────┘    │
│ ☑ 九成新   │                                            │
│ ○ 八成新   │ 分页: [上一页] 1 2 3 4 5 [下一页]          │
│            │ 共找到 128 本图书                           │
└─────────────────────────────────────────────────────────┘
```

**移动端适配**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏                                               │
├─────────────────────────────────────────────────────────┤
│ [筛选🔽] [排序🔽] [网格⊞] [列表☰]                        │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [图书卡片 - 横向布局]                               │ │
│ │ [图片] 书名                                ￥45.00  │ │
│ │        作者：张三                          [购买]   │ │
│ └─────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [图书卡片 - 横向布局]                               │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 3. 图书详情页设计

**整体布局**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (同首页)                                      │
├─────────────────────────────────────────────────────────┤
│ 面包屑导航                                               │
│ 首页 > 教材类 > 数学分析教程                             │
├─────────────────────────────────────────────────────────┤
│ 图书详情主体区域                                         │
│ ┌─────────────────┐ ┌─────────────────────────────────┐ │
│ │ 图片展示区域    │ │ 图书信息区域                    │ │
│ │ ┌─────────────┐ │ │ 数学分析教程                    │ │
│ │ │   主图片    │ │ │ 作者：张三                      │ │
│ │ │  400x500px  │ │ │ 出版社：高等教育出版社          │ │
│ │ └─────────────┘ │ │ ISBN：978-7-04-012345-6        │ │
│ │ [缩略图1][2][3] │ │ 分类：数学教材                  │ │
│ │                 │ │ 状况：九成新                    │ │
│ │                 │ │ ⭐⭐⭐⭐⭐ 4.8 (32条评价)        │ │
│ │                 │ │                                 │ │
│ │                 │ │ ￥45.00                        │ │
│ │                 │ │ 原价：￥89.00 节省：￥44.00     │ │
│ │                 │ │                                 │ │
│ │                 │ │ 数量：[- 1 +]                   │ │
│ │                 │ │                                 │ │
│ │                 │ │ [立即购买] [加入购物车]         │ │
│ │                 │ │                                 │ │
│ │                 │ │ 联系方式：                      │ │
│ │                 │ │ [💬在线] [📱微信] [🐧QQ] [📞电话] │ │
│ └─────────────────┘ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 详细信息标签页                                           │
│ [图书描述] [用户咨询] [购买须知]                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 图书描述内容区域                                    │ │
│ │ 这是一本经典的数学分析教程，适合大学数学专业...     │ │
│ │                                                     │ │
│ │ 目录：                                              │ │
│ │ 第一章 实数与函数                                   │ │
│ │ 第二章 数列极限                                     │ │
│ │ ...                                                 │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 用户咨询区域                                             │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 用户张三 2小时前                                    │ │
│ │ 这本书还有库存吗？什么时候能发货？                  │ │
│ │   └─ 管理员回复：有库存，今天下单明天发货          │ │
│ │                                                     │ │
│ │ 用户李四 1天前                                      │ │
│ │ 书的状况怎么样？有笔记吗？                          │ │
│ │   └─ 管理员回复：九成新，无笔记，很干净             │ │
│ │                                                     │ │
│ │ [发送咨询消息]                                      │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 请输入您的问题...                               │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │ [发送]                                              │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 4. 购物车页面设计

**整体布局**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (同首页)                                      │
├─────────────────────────────────────────────────────────┤
│ 购物车 (3件商品)                                         │
├─────────────────────────────────────────────────────────┤
│ ☑ 全选                                                  │
├─────────────────────────────────────────────────────────┤
│ ☑ [图片] 数学分析教程                    ￥45.00        │
│     作者：张三 | 状况：九成新                            │
│     数量：[- 1 +] [删除]                                 │
├─────────────────────────────────────────────────────────┤
│ ☑ [图片] 英语词典                        ￥30.00        │
│     作者：李四 | 状况：全新                              │
│     数量：[- 1 +] [删除]                                 │
├─────────────────────────────────────────────────────────┤
│ ○ [图片] 物理教程                        ￥25.00        │
│     作者：王五 | 状况：八成新                            │
│     数量：[- 1 +] [删除]                                 │
├─────────────────────────────────────────────────────────┤
│ 结算信息                                                 │
│ 商品总价：￥100.00                                       │
│ 运费：￥0.00                                            │
│ ─────────────────                                       │
│ 合计：￥75.00 (已选2件)                                  │
│                                                         │
│ [去结算]                                                │
└─────────────────────────────────────────────────────────┘
```

#### 5. 订单页面设计

**订单列表页**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (同首页)                                      │
├─────────────────────────────────────────────────────────┤
│ 我的订单                                                 │
│ [全部] [待支付] [待发货] [待收货] [已完成] [退货/售后]   │
├─────────────────────────────────────────────────────────┤
│ 订单 #20240001                          2024-01-15 10:30 │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [图片] 数学分析教程                        ￥45.00  │ │
│ │        作者：张三 | 数量：1                         │ │
│ │ [图片] 英语词典                            ￥30.00  │ │
│ │        作者：李四 | 数量：1                         │ │
│ │                                                     │ │
│ │ 订单状态：已支付，待发货                            │ │
│ │ 实付金额：￥75.00                                  │ │
│ │                                                     │ │
│ │ [查看详情] [联系客服] [申请退货]                    │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 订单 #20240002                          2024-01-14 15:20 │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [图片] 物理教程                            ￥25.00  │ │
│ │        作者：王五 | 数量：1                         │ │
│ │                                                     │ │
│ │ 订单状态：配送中                                    │ │
│ │ 配送员：张师傅 (138****1234)                       │ │
│ │ 实付金额：￥25.00                                  │ │
│ │                                                     │ │
│ │ [查看详情] [确认收货] [联系配送员]                  │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

**订单详情页**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (同首页)                                      │
├─────────────────────────────────────────────────────────┤
│ 订单详情 #20240001                                       │
├─────────────────────────────────────────────────────────┤
│ 订单状态                                                 │
│ ● 已下单 → ● 已支付 → ○ 已发货 → ○ 已送达               │
│ 2024-01-15  2024-01-15                                   │
│ 10:30       10:35                                        │
├─────────────────────────────────────────────────────────┤
│ 商品信息                                                 │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [图片] 数学分析教程                        ￥45.00  │ │
│ │        作者：张三 | 状况：九成新 | 数量：1          │ │
│ │ [图片] 英语词典                            ￥30.00  │ │
│ │        作者：李四 | 状况：全新 | 数量：1            │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 配送信息                                                 │
│ 收货人：张同学                                           │
│ 联系电话：138****5678                                   │
│ 收货地址：北京市朝阳区某某大学宿舍楼123号                │
│ 配送方式：平台配送                                       │
├─────────────────────────────────────────────────────────┤
│ 支付信息                                                 │
│ 商品总价：￥75.00                                       │
│ 运费：￥0.00                                            │
│ 优惠：-￥0.00                                           │
│ 实付金额：￥75.00                                       │
│ 支付方式：微信支付                                       │
│ 支付时间：2024-01-15 10:35                              │
├─────────────────────────────────────────────────────────┤
│ [联系客服] [申请退货] [再次购买]                         │
└─────────────────────────────────────────────────────────┘
```

#### 6. 个人中心设计

**个人中心首页**：
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (同首页)                                      │
├─────────────────────────────────────────────────────────┤
│ 个人信息区域                                             │
│ ┌─────────┐ 张同学                                      │
│ │ [头像]  │ 手机：138****5678                           │
│ │         │ 注册时间：2024-01-01                        │
│ └─────────┘ [编辑资料]                                  │
├─────────────────────────────────────────────────────────┤
│ 订单概览                                                 │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│ │待支付   │ │待发货   │ │待收货   │ │已完成   │        │
│ │   2     │ │   1     │ │   3     │ │   15    │        │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
├─────────────────────────────────────────────────────────┤
│ 功能菜单                                                 │
│ 📋 我的订单        >                                    │
│ ❤️ 我的收藏        >                                    │
│ 📍 收货地址        >                                    │
│ 🔒 账号安全        >                                    │
│ 💬 联系客服        >                                    │
│ ⚙️ 设置            >                                    │
│ 📤 退出登录        >                                    │
└─────────────────────────────────────────────────────────┘
```

#### 7. 登录注册页面设计

**登录页面**：
```
┌─────────────────────────────────────────────────────────┐
│ [← 返回]                                    [跳过登录]   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    📚 旧书买卖                          │
│                  欢迎回来！                             │
│                                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 手机号登录                                          │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 📱 请输入手机号                                 │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 🔒 请输入密码                                   │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │                                                     │ │ │
│ │ [忘记密码?]                                         │ │
│ │                                                     │ │ │
│ │ [登录]                                              │ │
│ │                                                     │ │ │
│ │ ──────────── 或 ────────────                       │ │
│ │                                                     │ │ │
│ │ [📱 微信登录] [🐧 QQ登录]                           │ │
│ │                                                     │ │ │
│ │ 还没有账号？[立即注册]                              │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

**注册页面**：
```
┌─────────────────────────────────────────────────────────┐
│ [← 返回]                                               │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    📚 旧书买卖                          │
│                   创建新账号                            │
│                                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 📱 请输入手机号                                 │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 📧 请输入邮箱 (可选)                            │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 🔒 设置密码                                     │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 🔒 确认密码                                     │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ │                                                     │ │ │
│ │ ☑ 我已阅读并同意《用户协议》和《隐私政策》          │ │
│ │                                                     │ │ │
│ │ [注册]                                              │ │
│ │                                                     │ │ │
│ │ ──────────── 或 ────────────                       │ │
│ │                                                     │ │ │
│ │ [📱 微信注册] [🐧 QQ注册]                           │ │
│ │                                                     │ │ │
│ │ 已有账号？[立即登录]                                │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 交互设计和用户体验

#### 交互状态设计

**按钮状态**：
```
正常状态：背景色#1890ff，文字白色
悬停状态：背景色#40a9ff，轻微阴影
点击状态：背景色#096dd9，按下效果
禁用状态：背景色#f5f5f5，文字#bfbfbf
加载状态：显示loading图标，禁用点击
```

**输入框状态**：
```
正常状态：边框#d9d9d9，背景白色
聚焦状态：边框#1890ff，蓝色阴影
错误状态：边框#ff4d4f，红色阴影
成功状态：边框#52c41a，绿色阴影
禁用状态：背景#f5f5f5，文字#bfbfbf
```

**加载状态设计**：
```
页面加载：骨架屏 (Skeleton)
数据加载：Spin组件
图片加载：占位图 + 渐入动画
按钮加载：loading图标 + 文字变化
```

#### 动画效果设计

**页面切换动画**：
```css
/* 淡入淡出 */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}

/* 滑动切换 */
.slide-enter {
  transform: translateX(100%);
}
.slide-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-out;
}
```

**微交互动画**：
```
按钮点击：scale(0.95) 100ms
卡片悬停：translateY(-2px) + 阴影加深
图片加载：opacity 0→1 渐入
消息提示：从顶部滑入
模态框：backdrop模糊 + 内容缩放
```

#### 响应式交互

**触摸手势 (移动端)**：
```
滑动刷新：下拉刷新页面
左右滑动：图片轮播、标签切换
长按：显示上下文菜单
双击：图片放大
捏合：图片缩放
```

**键盘快捷键 (桌面端)**：
```
Ctrl+F：聚焦搜索框
Enter：确认操作
Esc：关闭模态框
Tab：焦点切换
空格：滚动页面
```

### 错误处理和空状态设计

#### 错误页面设计

**404页面**：
```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│                    🔍                                   │
│                                                         │
│                 页面不存在                              │
│            抱歉，您访问的页面不存在                     │
│                                                         │
│              [返回首页] [联系客服]                       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

**网络错误页面**：
```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│                    📡                                   │
│                                                         │
│                 网络连接失败                            │
│            请检查网络连接后重试                         │
│                                                         │
│              [重新加载] [检查网络]                       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

#### 空状态设计

**购物车为空**：
```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│                    🛒                                   │
│                                                         │
│                购物车是空的                             │
│            快去挑选心仪的图书吧                         │
│                                                         │
│                [去逛逛]                                 │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

**搜索无结果**：
```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│                    🔍                                   │
│                                                         │
│              没有找到相关图书                           │
│          试试其他关键词或浏览分类                       │
│                                                         │
│            [浏览分类] [热门推荐]                         │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 性能优化设计

#### 图片优化
```
- 使用WebP格式，fallback到JPEG
- 响应式图片：不同尺寸设备加载不同分辨率
- 懒加载：intersection observer
- 占位图：base64编码的模糊图片
- 压缩：图片质量80%，尺寸优化
```

#### 代码优化
```
- 代码分割：路由级别的懒加载
- Tree shaking：移除未使用的代码
- 缓存策略：静态资源长期缓存
- CDN加速：静态资源CDN分发
- Gzip压缩：文本资源压缩
```

#### 用户体验优化
```
- 预加载：关键资源预加载
- 骨架屏：内容加载时的占位
- 渐进式加载：重要内容优先显示
- 离线支持：Service Worker缓存
- 错误边界：React错误边界处理
```

### 无障碍设计 (Accessibility)

#### 键盘导航
```
- Tab键顺序：逻辑清晰的焦点顺序
- 焦点指示：明显的焦点样式
- 跳过链接：快速跳转到主内容
- 键盘快捷键：常用操作的快捷键
```

#### 屏幕阅读器支持
```
- 语义化HTML：正确的标签使用
- ARIA标签：增强语义信息
- alt文本：图片的替代文本
- 标题层级：正确的h1-h6层级
```

#### 视觉辅助
```
- 颜色对比：符合WCAG标准
- 字体大小：最小14px
- 点击区域：最小44px×44px
- 颜色依赖：不仅依靠颜色传达信息
```

### 技术实现方案

#### 前端技术栈
```
核心框架：
- React 18 (函数组件 + Hooks)
- TypeScript (类型安全)
- Vite (构建工具)

UI框架：
- Ant Design (组件库)
- Styled Components (CSS-in-JS)
- Framer Motion (动画库)

状态管理：
- Zustand (轻量级状态管理)
- React Query (服务端状态管理)
- React Hook Form (表单管理)

路由和导航：
- React Router v6 (客户端路由)
- React Helmet (SEO优化)

工具库：
- Axios (HTTP客户端)
- Day.js (日期处理)
- Lodash (工具函数)
- Socket.io-client (实时通信)
```

#### 组件架构设计
```
src/
├── components/          # 通用组件
│   ├── ui/             # 基础UI组件
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Card/
│   │   └── Modal/
│   ├── layout/         # 布局组件
│   │   ├── Header/
│   │   ├── Footer/
│   │   ├── Sidebar/
│   │   └── Container/
│   └── business/       # 业务组件
│       ├── BookCard/
│       ├── OrderItem/
│       ├── SearchBox/
│       └── UserAvatar/
├── pages/              # 页面组件
│   ├── Home/
│   ├── BookList/
│   ├── BookDetail/
│   ├── Cart/
│   ├── Order/
│   ├── Profile/
│   └── Auth/
├── hooks/              # 自定义Hooks
│   ├── useAuth.ts
│   ├── useCart.ts
│   ├── useBooks.ts
│   └── useOrders.ts
├── services/           # API服务
│   ├── api.ts
│   ├── auth.ts
│   ├── books.ts
│   └── orders.ts
├── stores/             # 状态管理
│   ├── authStore.ts
│   ├── cartStore.ts
│   └── uiStore.ts
├── utils/              # 工具函数
│   ├── format.ts
│   ├── validation.ts
│   └── constants.ts
└── types/              # TypeScript类型
    ├── api.ts
    ├── book.ts
    └── user.ts
```

#### 核心功能实现

**路由配置**：
```typescript
// router.tsx
import { createBrowserRouter } from 'react-router-dom';
import { lazy } from 'react';

const Home = lazy(() => import('@/pages/Home'));
const BookList = lazy(() => import('@/pages/BookList'));
const BookDetail = lazy(() => import('@/pages/BookDetail'));
const Cart = lazy(() => import('@/pages/Cart'));
const Profile = lazy(() => import('@/pages/Profile'));

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      { index: true, element: <Home /> },
      { path: 'books', element: <BookList /> },
      { path: 'books/:id', element: <BookDetail /> },
      { path: 'cart', element: <Cart /> },
      { path: 'profile', element: <Profile /> },
    ],
  },
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      { path: 'login', element: <Login /> },
      { path: 'register', element: <Register /> },
    ],
  },
]);
```

**状态管理示例**：
```typescript
// stores/cartStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CartItem {
  id: string;
  title: string;
  price: number;
  quantity: number;
  image: string;
}

interface CartStore {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
  getTotalPrice: () => number;
  getTotalItems: () => number;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      addItem: (item) => set((state) => ({
        items: [...state.items, item]
      })),
      removeItem: (id) => set((state) => ({
        items: state.items.filter(item => item.id !== id)
      })),
      updateQuantity: (id, quantity) => set((state) => ({
        items: state.items.map(item =>
          item.id === id ? { ...item, quantity } : item
        )
      })),
      clearCart: () => set({ items: [] }),
      getTotalPrice: () => {
        const { items } = get();
        return items.reduce((total, item) =>
          total + item.price * item.quantity, 0
        );
      },
      getTotalItems: () => {
        const { items } = get();
        return items.reduce((total, item) =>
          total + item.quantity, 0
        );
      },
    }),
    { name: 'cart-storage' }
  )
);
```

**API服务示例**：
```typescript
// services/books.ts
import { api } from './api';

export interface Book {
  id: string;
  title: string;
  author: string;
  price: number;
  image: string;
  description: string;
  category: string;
  condition: string;
  stock: number;
}

export interface BookListParams {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: string;
}

export const booksApi = {
  // 获取图书列表
  getBooks: (params: BookListParams) =>
    api.get<{ books: Book[]; total: number }>('/books', { params }),

  // 获取图书详情
  getBook: (id: string) =>
    api.get<Book>(`/books/${id}`),

  // 搜索图书
  searchBooks: (query: string) =>
    api.get<Book[]>(`/books/search?q=${query}`),

  // 获取推荐图书
  getRecommendedBooks: () =>
    api.get<Book[]>('/books/recommended'),

  // 获取热门图书
  getPopularBooks: () =>
    api.get<Book[]>('/books/popular'),
};
```

#### 响应式设计实现

**CSS断点配置**：
```typescript
// utils/breakpoints.ts
export const breakpoints = {
  xs: '0px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  xxl: '1600px',
};

export const mediaQueries = {
  xs: `@media (min-width: ${breakpoints.xs})`,
  sm: `@media (min-width: ${breakpoints.sm})`,
  md: `@media (min-width: ${breakpoints.md})`,
  lg: `@media (min-width: ${breakpoints.lg})`,
  xl: `@media (min-width: ${breakpoints.xl})`,
  xxl: `@media (min-width: ${breakpoints.xxl})`,
};
```

**响应式组件示例**：
```typescript
// components/layout/Container.tsx
import styled from 'styled-components';
import { mediaQueries } from '@/utils/breakpoints';

export const Container = styled.div`
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;

  ${mediaQueries.sm} {
    max-width: 540px;
  }

  ${mediaQueries.md} {
    max-width: 720px;
  }

  ${mediaQueries.lg} {
    max-width: 960px;
  }

  ${mediaQueries.xl} {
    max-width: 1140px;
  }

  ${mediaQueries.xxl} {
    max-width: 1320px;
  }
`;

export const Grid = styled.div<{ cols?: number }>`
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;

  ${mediaQueries.sm} {
    grid-template-columns: repeat(2, 1fr);
  }

  ${mediaQueries.md} {
    grid-template-columns: repeat(3, 1fr);
  }

  ${mediaQueries.lg} {
    grid-template-columns: repeat(${props => props.cols || 4}, 1fr);
  }
`;
```
6. **管理后台**:
   - **普通管理员界面**：包含仪表板、图书管理、订单管理、配送管理、消息管理、个人设置等6大模块
   - **超级管理员界面**：继承所有管理员功能 + 管理员管理、操作监控、系统管理、数据分析、内容审核等专用模块

#### 管理员界面功能对比：

| 功能模块 | 普通管理员 | 超级管理员 |
|---------|-----------|-----------|
| 仪表板 | ✅ 工作概况 | ✅ 工作概况 + 系统概况 |
| 图书管理 | ✅ 完整功能 | ✅ 继承 + 审核权限 |
| 订单管理 | ✅ 完整功能 | ✅ 继承 + 数据分析 |
| 配送管理 | ✅ 完整功能 | ✅ 继承 + 统计分析 |
| 消息管理 | ✅ 完整功能 | ✅ 继承 + 全局审核 |
| 个人设置 | ✅ 基础设置 | ✅ 继承 + 高级设置 |
| 管理员管理 | ❌ | ✅ 专用功能 |
| 操作监控 | ❌ | ✅ 专用功能 |
| 系统管理 | ❌ | ✅ 专用功能 |
| 数据分析 | ❌ | ✅ 专用功能 |
| 内容审核 | ❌ | ✅ 专用功能 |

### 超级管理员界面设计详情

#### 主导航结构：
```
├── 仪表板 (Dashboard)
├── 管理员管理
│   ├── 管理员列表
│   ├── 创建管理员
│   ├── 权限管理
│   └── 登录历史
├── 操作监控
│   ├── 实时监控
│   ├── 操作日志
│   ├── 统计分析
│   └── 异常告警
├── 系统管理
│   ├── 系统配置
│   ├── 数据管理
│   ├── 系统维护
│   └── 性能监控
├── 数据分析
│   ├── 用户分析
│   ├── 图书分析
│   ├── 订单分析
│   └── 财务分析
├── 内容审核
│   ├── 消息审核
│   ├── 图书审核
│   └── 举报处理
└── 个人设置
```

#### 仪表板页面布局：
```
┌─────────────────────────────────────────────────────────┐
│ 系统概况卡片区                                            │
│ [在线用户] [管理员数] [今日订单] [系统状态]                  │
├─────────────────────────────────────────────────────────┤
│ 数据图表区                                               │
│ [用户趋势图]           [订单统计图]                       │
│ [销售分布图]           [操作活跃度图]                     │
├─────────────────────────────────────────────────────────┤
│ 实时监控区                                               │
│ [最近操作日志]         [系统告警面板]                     │
└─────────────────────────────────────────────────────────┘
```

#### 核心功能页面设计：
- **管理员管理页面**：表格列表、搜索筛选、批量操作、详情侧边栏、权限设置模态框
- **操作监控页面**：实时日志流、高级筛选器、统计图表、异常高亮、导出功能
- **数据分析页面**：多维度图表、时间选择器、数据对比、报表生成、自定义配置

## 测试策略

### 测试类型
- **单元测试**: Jest + React Testing Library
- **集成测试**: API接口测试
- **端到端测试**: Cypress (后期)
- **性能测试**: 负载测试工具

### 测试覆盖
- 用户认证流程
- 图书CRUD操作
- 订单处理流程
- 支付功能测试
- 退货流程测试（两天期限验证、状态流转）
- 图书消息功能测试（发送、回复、显示）
- 多种联系方式功能测试（选择、展示、跳转）
- 消息管理功能测试（删除、状态管理、审核）
- 管理员联系方式管理测试
- 实时通信测试
- 管理员权限控制测试
- 管理员操作日志记录测试
- 超级管理员管理功能测试
- 超级管理员仪表板功能测试
- 系统配置管理测试
- 数据分析功能测试
- 实时监控和告警测试
- 内容审核功能测试

## 部署考虑

### 本地部署（初期）
- Docker容器化部署
- 本地PostgreSQL数据库
- 本地文件存储

### 云部署（后期）
- 云服务器（阿里云/腾讯云）
- 云数据库服务
- CDN加速
- 负载均衡

## 项目里程碑

### 里程碑1: MVP完成 (3周后)
- 基础功能可用
- 用户可以浏览和购买图书
- 管理员可以管理图书和订单

### 里程碑2: 功能完善 (6周后)
- 所有核心功能完成
- 用户体验优化
- 系统稳定运行

### 里程碑3: 扩展优化 (8周后)
- C2C功能上线
- 性能优化完成
- 云部署就绪

## 风险评估和应对

### 技术风险
- **图书API限制**: 准备多个备用数据源
- **支付集成复杂**: 分阶段实现，先支持简单支付
- **实时功能性能**: 合理设计消息机制

### 业务风险
- **用户接受度**: 早期用户反馈收集
- **图书数据质量**: 建立数据审核机制

## 后续维护计划

### 功能迭代
- 根据用户反馈持续优化
- 新功能开发和上线
- 移动端APP开发

### 技术维护
- 定期安全更新
- 性能监控和优化
- 数据备份和恢复

---

**文档版本**: v1.0  
**创建日期**: 2025-07-29  
**最后更新**: 2025-07-29  
**负责人**: 开发团队
