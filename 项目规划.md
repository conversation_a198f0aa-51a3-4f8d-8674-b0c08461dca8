# 旧书买卖平台项目规划

## 项目概述

### 项目名称
大学生旧书买卖平台

### 项目目标
- 为大学生提供便捷的二手书买卖平台
- 采用B2C模式，平台收购后销售，后续扩展C2C功能
- 建立小型社区化的图书交易生态

### 目标用户
- 主要用户：大学生群体
- 用户规模：小型社区（预计500-2000用户）
- 使用场景：教材买卖、课外读物交换

## 技术栈选择

### 前端技术栈
- **框架**: React 18 + Vite
- **UI库**: Ant Design / Material-UI
- **状态管理**: Redux Toolkit / Zustand
- **路由**: React Router v6
- **HTTP客户端**: Axios
- **实时通信**: Socket.io-client

### 后端技术栈
- **运行环境**: Node.js 18+
- **框架**: Express.js
- **数据库**: PostgreSQL
- **ORM**: Prisma / Sequelize
- **身份验证**: JWT + bcrypt
- **短信服务**: 阿里云短信服务 / 腾讯云短信
- **第三方登录**: 微信开放平台SDK + QQ互联SDK
- **实时通信**: Socket.io
- **文件上传**: Multer + 云存储

### 开发工具
- **版本控制**: Git
- **包管理**: npm/yarn
- **代码规范**: ESLint + Prettier
- **容器化**: Docker
- **API文档**: Swagger/OpenAPI

## 核心功能模块

### 1. 用户管理模块
- **用户注册/登录**
  - 手机号注册（主要方式，后续开发短信验证码）
  - 邮箱注册验证（备用方式）
  - 密码找回功能
  - 第三方登录（微信/QQ，点击后跳转到对应平台登录页面）

- **用户角色**
  - 普通用户（买家）
  - 管理员（图书录入、订单管理，可有多个，由超级管理员创建）
  - 超级管理员（系统管理、管理员账号管理、操作监控）

### 2. 图书管理模块
- **图书录入**（管理员功能）
  - ISBN扫码录入
  - 手动信息录入
  - 图书信息自动补全（调用图书API）
  - 图书照片上传
  - 价格设定
  - 库存管理

- **图书信息展示**
  - 图书详情页
  - 图书列表页
  - 分类浏览
  - 搜索功能（标题、作者、ISBN）
  - 筛选功能（价格、分类、状况）

### 3. 交易流程模块
- **购买流程**
  - 加入购物车
  - 订单确认
  - 支付选择（线上/线下）
  - 配送方式选择

- **订单管理**
  - 订单状态跟踪（待支付/已支付/配送中/已送达）
  - 配送中显示配送员联系电话
  - 订单历史查看
  - 退款/退货处理

### 4. 支付模块
- **支付方式**
  - 线上支付（支付宝/微信支付）
  - 线下支付（现金/转账）
  - 支付状态管理

### 5. 物流配送模块
- **配送方式**
  - 平台配送（当前）
  - 自取功能（后期）

- **配送状态管理**
  - 配送中（显示配送员联系电话）
  - 已送达（配送完成）

### 6. 消息通信模块
- **实时聊天**
  - 用户与管理员沟通
  - 订单相关咨询
  - 系统通知推送

### 7. 管理员管理模块（超级管理员专用）
- **管理员账号管理**
  - 创建管理员账号（确保用户名唯一）
  - 管理员账号列表查看
  - 管理员账号状态管理（启用/禁用）
  - 管理员权限设置

- **操作监控系统**
  - 管理员操作日志记录
  - 操作日志查询和筛选
  - 操作统计和分析
  - 异常操作告警

## 数据库设计

### 核心数据表

#### 用户表 (users)
```sql
- id (主键)
- username (用户名，管理员账号唯一)
- email (邮箱，可选)
- phone (手机号，主要注册方式)
- password_hash (密码哈希)
- role (角色: user/admin/super_admin)
- avatar (头像URL)
- register_type (注册类型: phone/email/wechat/qq)
- third_party_id (第三方平台用户ID，用于微信/QQ登录)
- phone_verified (手机号验证状态)
- email_verified (邮箱验证状态)
- status (账户状态: active/inactive/banned)
- created_by (创建者ID，记录是哪个超级管理员创建的管理员账号)
- last_login_at (最后登录时间)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 图书表 (books)
```sql
- id (主键)
- isbn (ISBN号)
- title (书名)
- author (作者)
- publisher (出版社)
- publication_date (出版日期)
- category_id (分类ID)
- description (描述)
- condition (书籍状况)
- price (价格)
- stock (库存)
- cover_image (封面图片)
- images (其他图片)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 订单表 (orders)
```sql
- id (主键)
- user_id (用户ID)
- total_amount (总金额)
- status (订单状态)
- payment_method (支付方式)
- payment_status (支付状态)
- delivery_method (配送方式)
- delivery_address (配送地址)
- delivery_status (配送状态: pending/delivering/delivered)
- delivery_phone (配送员联系电话)
- delivery_person (配送员姓名)
- notes (备注)
- created_at (创建时间)
- updated_at (更新时间)
```

#### 订单详情表 (order_items)
```sql
- id (主键)
- order_id (订单ID)
- book_id (图书ID)
- quantity (数量)
- price (单价)
- subtotal (小计)
```

#### 分类表 (categories)
```sql
- id (主键)
- name (分类名称)
- description (描述)
- parent_id (父分类ID)
```

#### 消息表 (messages)
```sql
- id (主键)
- sender_id (发送者ID)
- receiver_id (接收者ID)
- order_id (关联订单ID，可选)
- content (消息内容)
- type (消息类型)
- read_status (已读状态)
- created_at (创建时间)
```

#### 管理员操作日志表 (admin_logs)
```sql
- id (主键)
- admin_id (管理员ID)
- action_type (操作类型: create/update/delete/login/logout)
- target_type (操作对象类型: book/order/user)
- target_id (操作对象ID)
- action_description (操作描述)
- ip_address (操作IP地址)
- user_agent (用户代理)
- request_data (请求数据，JSON格式)
- result (操作结果: success/failed)
- created_at (操作时间)
```

## 开发阶段规划

### 第一阶段：MVP核心功能（2-3周）
**目标**: 实现基础的图书买卖功能

**功能清单**:
- [ ] 项目初始化和环境搭建
- [ ] 用户注册/登录系统
- [ ] 基础图书管理（录入、展示）
- [ ] 简单搜索功能
- [ ] 基础购买流程
- [ ] 订单管理基础功能
- [ ] 基础支付集成

**技术任务**:
- [ ] 数据库设计和创建
- [ ] 后端API基础架构
- [ ] 前端项目初始化
- [ ] 用户认证系统（手机号注册 + 第三方登录跳转）
- [ ] 短信服务集成（后期开发验证码）
- [ ] 微信/QQ第三方登录SDK集成
- [ ] 图书信息API集成

### 第二阶段：功能完善（2-3周）
**目标**: 完善用户体验和管理功能

**功能清单**:
- [ ] 高级搜索和筛选
- [ ] 用户个人中心
- [ ] 完整订单管理系统
- [ ] 实时聊天功能
- [ ] 图书分类管理
- [ ] 管理员账号管理系统（超级管理员功能）
- [ ] 管理员操作监控和日志系统
- [ ] 管理员后台完善
- [ ] 支付功能完善

### 第三阶段：优化和扩展（1-2周）
**目标**: 性能优化和功能扩展

**功能清单**:
- [ ] 性能优化
- [ ] C2C功能基础架构
- [ ] 自取功能开发
- [ ] 移动端适配
- [ ] 云部署准备

## 图书信息数据源

### 推荐API服务
1. **豆瓣图书API** (主要)
   - 丰富的图书信息
   - 包含评分和评论
   - 免费使用（有限制）

2. **开放图书馆API** (备用)
   - Open Library API
   - 国际图书数据

3. **ISBN查询服务** (补充)
   - 基础图书信息查询
   - 多个免费服务可选

## 用户界面设计

### 设计原则
- 简洁明了的界面设计
- 移动端友好的响应式设计
- 符合大学生使用习惯
- 快速加载和流畅交互

### 主要页面
1. **首页**: 图书推荐、搜索入口、分类导航
2. **图书列表页**: 搜索结果、分类浏览
3. **图书详情页**: 详细信息、购买按钮
4. **购物车页**: 商品管理、结算
5. **个人中心**: 订单管理、个人信息
6. **管理后台**:
   - 普通管理员：图书管理、订单处理、配送管理（分配配送员、更新配送状态）
   - 超级管理员：管理员账号管理、操作监控、系统管理

## 测试策略

### 测试类型
- **单元测试**: Jest + React Testing Library
- **集成测试**: API接口测试
- **端到端测试**: Cypress (后期)
- **性能测试**: 负载测试工具

### 测试覆盖
- 用户认证流程
- 图书CRUD操作
- 订单处理流程
- 支付功能测试
- 实时通信测试
- 管理员权限控制测试
- 管理员操作日志记录测试
- 超级管理员管理功能测试

## 部署考虑

### 本地部署（初期）
- Docker容器化部署
- 本地PostgreSQL数据库
- 本地文件存储

### 云部署（后期）
- 云服务器（阿里云/腾讯云）
- 云数据库服务
- CDN加速
- 负载均衡

## 项目里程碑

### 里程碑1: MVP完成 (3周后)
- 基础功能可用
- 用户可以浏览和购买图书
- 管理员可以管理图书和订单

### 里程碑2: 功能完善 (6周后)
- 所有核心功能完成
- 用户体验优化
- 系统稳定运行

### 里程碑3: 扩展优化 (8周后)
- C2C功能上线
- 性能优化完成
- 云部署就绪

## 风险评估和应对

### 技术风险
- **图书API限制**: 准备多个备用数据源
- **支付集成复杂**: 分阶段实现，先支持简单支付
- **实时功能性能**: 合理设计消息机制

### 业务风险
- **用户接受度**: 早期用户反馈收集
- **图书数据质量**: 建立数据审核机制

## 后续维护计划

### 功能迭代
- 根据用户反馈持续优化
- 新功能开发和上线
- 移动端APP开发

### 技术维护
- 定期安全更新
- 性能监控和优化
- 数据备份和恢复

---

**文档版本**: v1.0  
**创建日期**: 2025-07-29  
**最后更新**: 2025-07-29  
**负责人**: 开发团队
